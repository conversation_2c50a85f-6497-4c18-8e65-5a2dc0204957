# Decorator Design Pattern - Python Implementation

This directory contains a Python implementation of the Decorator Design Pattern, equivalent to the PHP implementation in `../PHP/`.

## Pattern Overview

The Decorator Design Pattern allows behavior to be added to objects dynamically without altering their structure. It provides a flexible alternative to subclassing for extending functionality.

## Implementation Structure

### Core Components

- **`service.py`** - Abstract base class defining the service interface
- **`base_service.py`** - Concrete implementation of the service
- **Decorators:**
  - **`auth_decorator.py`** - Adds authentication checking
  - **`cache_decorator.py`** - Adds caching functionality
  - **`logger_decorator.py`** - Adds logging before/after execution

### Demo and Testing

- **`main.py`** - Demonstration script showing decorator composition
- **`test_decorators.py`** - Unit tests for all components
- **`__init__.py`** - Package initialization file

## Key Features

1. **Interface Compliance**: All decorators implement the same `Service` interface
2. **Composition**: Decorators can be chained together in any order
3. **Transparency**: Decorated objects can be used wherever the original object is expected
4. **Dynamic Behavior**: Functionality can be added/removed at runtime

## Usage Example

```python
from base_service import BaseService
from logger_decorator import LoggerDecorator
from auth_decorator import AuthDecorator
from cache_decorator import CacheDecorator

# Create base service
service = BaseService()

# Add decorators
service = LoggerDecorator(service)
service = AuthDecorator(service, authenticated=True)
service = CacheDecorator(service)

# Execute with all decorations
result = service.execute()
```

## Running the Code

### Run the demonstration:
```bash
python3 main.py
```

### Run the tests:
```bash
python3 test_decorators.py
```

## Differences from PHP Implementation

1. **Abstract Base Classes**: Uses Python's `abc` module instead of PHP interfaces
2. **Type Hints**: Includes type annotations for better code clarity
3. **Naming Conventions**: Uses Python snake_case naming
4. **Documentation**: Includes comprehensive docstrings
5. **Testing**: Includes unit tests using Python's `unittest` framework

## Design Benefits

- **Single Responsibility**: Each decorator has one specific concern
- **Open/Closed Principle**: Open for extension, closed for modification
- **Composition over Inheritance**: Uses composition to add functionality
- **Runtime Flexibility**: Decorators can be applied dynamically
