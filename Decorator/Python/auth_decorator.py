"""
Authentication decorator for the Decorator Design Pattern implementation.
"""

from service import Service


class AuthDecorator(Service):
    """Decorator that adds authentication checking to a service."""
    
    def __init__(self, wrapped: Service, authenticated: bool):
        """
        Initialize the authentication decorator.
        
        Args:
            wrapped: The service to wrap
            authenticated: Whether the user is authenticated
        """
        self._wrapped = wrapped
        self._authenticated = authenticated
    
    def execute(self) -> str:
        """Execute the service with authentication checking."""
        if not self._authenticated:
            return "[Auth] Access denied."
        return self._wrapped.execute()
