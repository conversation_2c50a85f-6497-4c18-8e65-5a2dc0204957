"""
Cache decorator for the Decorator Design Pattern implementation.
"""

from typing import Optional
from service import Service


class CacheDecorator(Service):
    """Decorator that adds caching functionality to a service."""
    
    def __init__(self, wrapped: Service):
        """
        Initialize the cache decorator.
        
        Args:
            wrapped: The service to wrap
        """
        self._wrapped = wrapped
        self._cached_result: Optional[str] = None
    
    def execute(self) -> str:
        """Execute the service with caching."""
        if self._cached_result is not None:
            return f"[Cache] Returning from cache: {self._cached_result}"
        
        self._cached_result = self._wrapped.execute()
        return f"[Cache] Caching result: {self._cached_result}"
