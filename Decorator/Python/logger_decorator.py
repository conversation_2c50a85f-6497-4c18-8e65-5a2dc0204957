"""
Logger decorator for the Decorator Design Pattern implementation.
"""

from service import Service


class LoggerDecorator(Service):
    """Decorator that adds logging functionality to a service."""
    
    def __init__(self, wrapped: Service):
        """
        Initialize the logger decorator.
        
        Args:
            wrapped: The service to wrap
        """
        self._wrapped = wrapped
    
    def execute(self) -> str:
        """Execute the service with logging."""
        print("[Logger] Before execution")
        result = self._wrapped.execute()
        print("[Logger] After execution")
        return result
