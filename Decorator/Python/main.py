"""
Main demonstration script for the Decorator Design Pattern implementation.
"""

from base_service import BaseService
from logger_decorator import LoggerDecorator
from cache_decorator import CacheDecorator
from auth_decorator import AuthDecorator


def main():
    """Demonstrate the Decorator Design Pattern."""
    print("=== Authenticated User ===")
    # Compose decorators with authenticated user
    service = BaseService()
    with_logging = LoggerDecorator(service)
    with_auth = AuthDecorator(with_logging, True)
    with_cache = CacheDecorator(with_auth)

    # Execute the service
    print(with_cache.execute())

    # Second call should hit cache
    print(with_cache.execute())

    print("\n=== Unauthenticated User ===")
    # Test with unauthenticated user
    service2 = BaseService()
    with_logging2 = LoggerDecorator(service2)
    with_auth2 = AuthDecorator(with_logging2, False)
    with_cache2 = CacheDecorator(with_auth2)

    print(with_cache2.execute())


if __name__ == "__main__":
    main()
