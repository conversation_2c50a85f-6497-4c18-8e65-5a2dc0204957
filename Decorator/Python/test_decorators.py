"""
Unit tests for the Decorator Design Pattern implementation.
"""

import unittest
from io import StringIO
import sys

from service import Service
from base_service import BaseService
from auth_decorator import AuthDecorator
from cache_decorator import CacheDecorator
from logger_decorator import LoggerDecorator


class TestDecoratorPattern(unittest.TestCase):
    """Test cases for the decorator pattern implementation."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.base_service = BaseService()
    
    def test_base_service(self):
        """Test the base service functionality."""
        result = self.base_service.execute()
        self.assertEqual(result, "Executing base service.")
    
    def test_auth_decorator_authenticated(self):
        """Test authentication decorator with authenticated user."""
        auth_service = AuthDecorator(self.base_service, True)
        result = auth_service.execute()
        self.assertEqual(result, "Executing base service.")
    
    def test_auth_decorator_unauthenticated(self):
        """Test authentication decorator with unauthenticated user."""
        auth_service = AuthDecorator(self.base_service, False)
        result = auth_service.execute()
        self.assertEqual(result, "[Auth] Access denied.")
    
    def test_cache_decorator_first_call(self):
        """Test cache decorator on first call."""
        cache_service = CacheDecorator(self.base_service)
        result = cache_service.execute()
        self.assertEqual(result, "[Cache] Caching result: Executing base service.")
    
    def test_cache_decorator_second_call(self):
        """Test cache decorator on second call (should return from cache)."""
        cache_service = CacheDecorator(self.base_service)
        # First call
        cache_service.execute()
        # Second call should return from cache
        result = cache_service.execute()
        self.assertEqual(result, "[Cache] Returning from cache: Executing base service.")
    
    def test_logger_decorator(self):
        """Test logger decorator functionality."""
        # Capture stdout to test logging
        captured_output = StringIO()
        sys.stdout = captured_output
        
        logger_service = LoggerDecorator(self.base_service)
        result = logger_service.execute()
        
        # Restore stdout
        sys.stdout = sys.__stdout__
        
        # Check the result
        self.assertEqual(result, "Executing base service.")
        
        # Check the logged output
        output = captured_output.getvalue()
        self.assertIn("[Logger] Before execution", output)
        self.assertIn("[Logger] After execution", output)
    
    def test_decorator_composition(self):
        """Test composition of multiple decorators."""
        # Compose decorators: Base -> Logger -> Auth -> Cache
        service = BaseService()
        with_logging = LoggerDecorator(service)
        with_auth = AuthDecorator(with_logging, True)
        with_cache = CacheDecorator(with_auth)
        
        # Capture stdout for logger
        captured_output = StringIO()
        sys.stdout = captured_output
        
        # First call
        result1 = with_cache.execute()
        
        # Restore stdout
        sys.stdout = sys.__stdout__
        
        # Check first call result
        self.assertEqual(result1, "[Cache] Caching result: Executing base service.")
        
        # Check logging occurred
        output = captured_output.getvalue()
        self.assertIn("[Logger] Before execution", output)
        self.assertIn("[Logger] After execution", output)
        
        # Second call should hit cache (no logging this time)
        result2 = with_cache.execute()
        self.assertEqual(result2, "[Cache] Returning from cache: Executing base service.")


if __name__ == "__main__":
    unittest.main()
